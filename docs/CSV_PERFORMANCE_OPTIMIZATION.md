# CSV書き込みパフォーマンス最適化ガイド

## 概要

CsvWriteStrategyクラスに実装されたパフォーマンス最適化機能の使用方法と設定について説明します。

## 最適化機能

### 1. バッチ書き込み機能
- **機能**: 複数のレコードをバッファに蓄積してから一括書き込み
- **効果**: I/O操作回数を削減し、書き込み性能を向上
- **設定**: `csvWriteBufferSize`でバッファサイズを指定

### 2. オブジェクトプール
- **機能**: 文字列配列の再利用によるメモリ効率向上
- **効果**: ガベージコレクション負荷を軽減
- **設定**: `enableArrayReuse`で有効/無効を切り替え

### 3. 拡張バッファリング
- **機能**: 出力ストリームのバッファサイズを最適化
- **効果**: ファイルI/O効率を向上
- **設定**: `outputStreamBufferSize`でバッファサイズを指定

### 4. フィールドマッピングキャッシュ
- **機能**: フィールド名マッピングの結果をキャッシュ
- **効果**: 繰り返し処理の高速化
- **設定**: 自動的に有効化（フィールドマッピング使用時）

## 設定例

### 標準設定（推奨）
```java
ExportOptions options = ExportOptions.builder()
    .format(FileFormat.CSV)
    .includeHeader(true)
    .columns(columns)
    .csvWriteBufferSize(1000)           // 1000レコードでバッチ書き込み
    .outputStreamBufferSize(32768)      // 32KBバッファ
    .enableBatchWrite(true)             // バッチ書き込み有効
    .enableArrayReuse(true)             // オブジェクトプール有効
    .build();
```

### 高性能設定（大容量ファイル用）
```java
ExportOptions options = ExportOptions.builder()
    .format(FileFormat.CSV)
    .includeHeader(true)
    .columns(columns)
    .csvWriteBufferSize(2000)           // バッファサイズを2倍に
    .outputStreamBufferSize(65536)      // 64KBバッファ
    .enableBatchWrite(true)
    .enableArrayReuse(true)
    .build();
```

### 省メモリ設定（メモリ制約環境用）
```java
ExportOptions options = ExportOptions.builder()
    .format(FileFormat.CSV)
    .includeHeader(true)
    .columns(columns)
    .csvWriteBufferSize(500)            // バッファサイズを削減
    .outputStreamBufferSize(16384)      // 16KBバッファ
    .enableBatchWrite(true)
    .enableArrayReuse(false)            // オブジェクトプール無効
    .build();
```

### 互換性重視設定（既存システム互換）
```java
ExportOptions options = ExportOptions.builder()
    .format(FileFormat.CSV)
    .includeHeader(true)
    .columns(columns)
    .csvWriteBufferSize(1)              // バッチ書き込み実質無効
    .outputStreamBufferSize(8192)       // デフォルトバッファサイズ
    .enableBatchWrite(false)            // 最適化機能無効
    .enableArrayReuse(false)
    .build();
```

## パフォーマンス設定ガイドライン

### データ量別推奨設定

| データ量 | csvWriteBufferSize | outputStreamBufferSize | enableArrayReuse |
|---------|-------------------|----------------------|------------------|
| ~10,000件 | 500 | 16KB | false |
| ~100,000件 | 1,000 | 32KB | true |
| 100,000件~ | 2,000 | 64KB | true |

### メモリ制約別推奨設定

| メモリ制約 | 設定方針 | 推奨バッファサイズ |
|-----------|---------|------------------|
| 厳しい | 省メモリ設定 | 500レコード, 16KB |
| 普通 | 標準設定 | 1,000レコード, 32KB |
| 緩い | 高性能設定 | 2,000レコード, 64KB |

## 使用方法

### 1. 設定例クラスの使用
```java
// 標準設定を使用
ExportOptions options = CsvPerformanceConfigExample.createStandardConfig();

// カスタム設定を使用
ExportOptions options = CsvPerformanceConfigExample.createCustomConfig(50000, 2);
```

### 2. 既存サービスでの適用
```java
@Override
protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey) {
    return ExportOptions.builder()
        .format(FileFormat.CSV)
        .includeHeader(true)
        .columns(CSV_COLUMNS)
        .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
        // パフォーマンス最適化設定を追加
        .csvWriteBufferSize(BusinessConstants.CSV_WRITE_BUFFER_SIZE)
        .outputStreamBufferSize(BusinessConstants.OUTPUT_STREAM_BUFFER_SIZE)
        .enableBatchWrite(true)
        .enableArrayReuse(true)
        .enableFieldMapping(true)
        .fieldHeaderMapping(createFieldHeaderMapping())
        .build();
}
```

## 注意事項

### 1. メモリ使用量
- バッファサイズを大きくするとメモリ使用量が増加
- Lambda環境では適切なサイズ設定が重要

### 2. 互換性
- 既存のDataWriteStrategyインターフェースは変更なし
- デフォルト設定では既存動作と同等

### 3. エラーハンドリング
- バッファ書き込み失敗時は自動的にフォールバック
- リソースクリーンアップは自動実行

## パフォーマンス測定

### テスト実行
```bash
mvn test -Dtest=CsvWriteStrategyPerformanceTest
```

### 期待される改善効果
- **I/O効率**: 50-70%の書き込み回数削減
- **メモリ効率**: 30-50%のオブジェクト生成削減
- **全体性能**: 30-60%の処理時間短縮（データ量により変動）

## トラブルシューティング

### メモリ不足エラー
- `csvWriteBufferSize`を削減
- `enableArrayReuse`を無効化

### 性能が改善されない場合
- `outputStreamBufferSize`を増加
- `enableBatchWrite`が有効か確認

### 互換性問題
- 互換性重視設定を使用
- 段階的に最適化機能を有効化
