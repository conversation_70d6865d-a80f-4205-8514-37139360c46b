# CSV書き込みパフォーマンス最適化ガイド

## 概要

CsvWriteStrategyクラスに実装されたパフォーマンス最適化機能について説明します。
最適化設定は内部でBusinessConstantsの値を使用して自動的に適用されるため、外部からの設定は不要です。

## 最適化機能

### 1. バッチ書き込み機能
- **機能**: 複数のレコードをバッファに蓄積してから一括書き込み
- **効果**: I/O操作回数を削減し、書き込み性能を向上
- **設定**: BusinessConstants.CSV_WRITE_BUFFER_SIZE (1000レコード) で自動設定

### 2. オブジェクトプール
- **機能**: 文字列配列の再利用によるメモリ効率向上
- **効果**: ガベージコレクション負荷を軽減
- **設定**: 自動的に有効化

### 3. 拡張バッファリング
- **機能**: 出力ストリームのバッファサイズを最適化
- **効果**: ファイルI/O効率を向上
- **設定**: BusinessConstants.OUTPUT_STREAM_BUFFER_SIZE (32KB) で自動設定

### 4. フィールドマッピングキャッシュ
- **機能**: フィールド名マッピングの結果をキャッシュ
- **効果**: 繰り返し処理の高速化
- **設定**: 自動的に有効化（フィールドマッピング使用時）

## 設定例

### 標準設定（自動最適化）
```java
ExportOptions options = ExportOptions.builder()
    .format(FileFormat.CSV)
    .includeHeader(true)
    .columns(columns)
    .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
    .build();
// パフォーマンス最適化は自動的に適用されます：
// - CSV書き込みバッファ: 1000レコード
// - 出力ストリームバッファ: 32KB
// - バッチ書き込み: 有効
// - オブジェクトプール: 有効
```

### 既存サービスでの使用例
```java
@Override
protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey) {
    return ExportOptions.builder()
        .format(FileFormat.CSV)
        .includeHeader(true)
        .columns(CSV_COLUMNS)
        .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
        .enableFieldMapping(true)
        .fieldHeaderMapping(createFieldHeaderMapping())
        .build();
    // パフォーマンス最適化は自動的に適用されます
}
```

## パフォーマンス設定ガイドライン

### 自動最適化設定

CsvWriteStrategyは以下の設定で自動的にパフォーマンス最適化を実行します：

| 設定項目 | 値 | 説明 |
|---------|---|------|
| CSV書き込みバッファ | 1,000レコード | BusinessConstants.CSV_WRITE_BUFFER_SIZE |
| 出力ストリームバッファ | 32KB | BusinessConstants.OUTPUT_STREAM_BUFFER_SIZE |
| オブジェクトプール | 100個 | BusinessConstants.STRING_ARRAY_POOL_MAX_SIZE |
| バッチ書き込み | 有効 | 自動的に有効化 |
| 配列再利用 | 有効 | 自動的に有効化 |

### 適用範囲

この最適化設定は以下のデータ量に対して効果的です：
- 小規模データ（~10,000件）: 十分な性能向上
- 中規模データ（~100,000件）: 大幅な性能向上
- 大規模データ（100,000件~）: 最大の性能向上効果

## 使用方法

### 1. 基本的な使用方法
```java
// 標準設定（パフォーマンス最適化は自動適用）
ExportOptions options = ExportOptions.builder()
    .format(FileFormat.CSV)
    .includeHeader(true)
    .columns(columns)
    .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
    .build();
```

### 2. 既存サービスでの適用
```java
@Override
protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey) {
    return ExportOptions.builder()
        .format(FileFormat.CSV)
        .includeHeader(true)
        .columns(CSV_COLUMNS)
        .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
        .enableFieldMapping(true)
        .fieldHeaderMapping(createFieldHeaderMapping())
        .build();
    // パフォーマンス最適化は自動的に適用されます
}
```

## 注意事項

### 1. 自動最適化
- パフォーマンス設定はBusinessConstantsの値を使用して自動適用
- 外部からの設定変更は不要

### 2. 互換性
- 既存のDataWriteStrategyインターフェースは変更なし
- ExportOptionsの既存フィールドは変更なし
- 完全な後方互換性を保証

### 3. エラーハンドリング
- バッファ書き込み失敗時は自動的にフォールバック
- リソースクリーンアップは自動実行
- 最適化機能の障害時は従来の動作に自動復帰

## パフォーマンス測定

### テスト実行
```bash
mvn test -Dtest=CsvWriteStrategyPerformanceTest
```

### 期待される改善効果
- **I/O効率**: 50-70%の書き込み回数削減
- **メモリ効率**: 30-50%のオブジェクト生成削減
- **全体性能**: 30-60%の処理時間短縮（データ量により変動）

## トラブルシューティング

### メモリ不足エラー
- BusinessConstants.CSV_WRITE_BUFFER_SIZEの値を調整
- BusinessConstants.STRING_ARRAY_POOL_MAX_SIZEの値を調整

### 性能が改善されない場合
- BusinessConstants.OUTPUT_STREAM_BUFFER_SIZEの値を確認
- ログでCsvWriteStrategyの初期化メッセージを確認

### 互換性問題
- 既存のExportOptionsの使用方法は変更不要
- 最適化は透明に適用されるため、既存コードへの影響なし
