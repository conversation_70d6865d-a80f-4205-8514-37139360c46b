package com.ms.bp.shared.common.io.strategy.impl;

import com.ms.bp.shared.common.io.config.CsvPerformanceConfigExample;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CsvWriteStrategy パフォーマンステスト
 * 最適化機能の効果を検証
 */
class CsvWriteStrategyPerformanceTest {
    private static final Logger logger = LoggerFactory.getLogger(CsvWriteStrategyPerformanceTest.class);
    
    private CsvWriteStrategy<Map<String, Object>> strategy;
    private List<String> columns;
    private List<Map<String, Object>> testData;
    
    @BeforeEach
    void setUp() {
        strategy = new CsvWriteStrategy<>();
        
        // テスト用列定義
        columns = Arrays.asList("id", "name", "email", "department", "salary");
        
        // テストデータ生成（1000件）
        testData = generateTestData(1000);
    }
    
    @Test
    @DisplayName("標準設定でのCSV書き込み性能テスト")
    void testStandardPerformance() throws IOException {
        // 標準設定を使用
        ExportOptions options = CsvPerformanceConfigExample.createStandardConfig();
        options = ExportOptions.builder()
                .format(options.getFormat())
                .includeHeader(options.isIncludeHeader())
                .columns(columns)
                .csvWriteBufferSize(options.getCsvWriteBufferSize())
                .outputStreamBufferSize(options.getOutputStreamBufferSize())
                .enableBatchWrite(options.isEnableBatchWrite())
                .enableArrayReuse(options.isEnableArrayReuse())
                .build();
        
        // パフォーマンステスト実行
        PerformanceResult result = executePerformanceTest(options, "標準設定");
        
        // 結果検証
        assertNotNull(result);
        assertTrue(result.executionTime > 0);
        assertTrue(result.outputSize > 0);
        assertEquals(testData.size(), result.recordCount);
        
        logger.info("標準設定テスト完了: {}", result);
    }
    
    @Test
    @DisplayName("高性能設定でのCSV書き込み性能テスト")
    void testHighPerformanceConfig() throws IOException {
        // 高性能設定を使用
        ExportOptions options = CsvPerformanceConfigExample.createHighPerformanceConfig();
        options = ExportOptions.builder()
                .format(options.getFormat())
                .includeHeader(options.isIncludeHeader())
                .columns(columns)
                .csvWriteBufferSize(options.getCsvWriteBufferSize())
                .outputStreamBufferSize(options.getOutputStreamBufferSize())
                .enableBatchWrite(options.isEnableBatchWrite())
                .enableArrayReuse(options.isEnableArrayReuse())
                .build();
        
        // パフォーマンステスト実行
        PerformanceResult result = executePerformanceTest(options, "高性能設定");
        
        // 結果検証
        assertNotNull(result);
        assertTrue(result.executionTime > 0);
        assertTrue(result.outputSize > 0);
        assertEquals(testData.size(), result.recordCount);
        
        logger.info("高性能設定テスト完了: {}", result);
    }
    
    @Test
    @DisplayName("省メモリ設定でのCSV書き込み性能テスト")
    void testLowMemoryConfig() throws IOException {
        // 省メモリ設定を使用
        ExportOptions options = CsvPerformanceConfigExample.createLowMemoryConfig();
        options = ExportOptions.builder()
                .format(options.getFormat())
                .includeHeader(options.isIncludeHeader())
                .columns(columns)
                .csvWriteBufferSize(options.getCsvWriteBufferSize())
                .outputStreamBufferSize(options.getOutputStreamBufferSize())
                .enableBatchWrite(options.isEnableBatchWrite())
                .enableArrayReuse(options.isEnableArrayReuse())
                .build();
        
        // パフォーマンステスト実行
        PerformanceResult result = executePerformanceTest(options, "省メモリ設定");
        
        // 結果検証
        assertNotNull(result);
        assertTrue(result.executionTime > 0);
        assertTrue(result.outputSize > 0);
        assertEquals(testData.size(), result.recordCount);
        
        logger.info("省メモリ設定テスト完了: {}", result);
    }
    
    @Test
    @DisplayName("互換性重視設定でのCSV書き込み性能テスト")
    void testCompatibilityConfig() throws IOException {
        // 互換性重視設定を使用
        ExportOptions options = CsvPerformanceConfigExample.createCompatibilityConfig();
        options = ExportOptions.builder()
                .format(options.getFormat())
                .includeHeader(options.isIncludeHeader())
                .columns(columns)
                .csvWriteBufferSize(options.getCsvWriteBufferSize())
                .outputStreamBufferSize(options.getOutputStreamBufferSize())
                .enableBatchWrite(options.isEnableBatchWrite())
                .enableArrayReuse(options.isEnableArrayReuse())
                .build();
        
        // パフォーマンステスト実行
        PerformanceResult result = executePerformanceTest(options, "互換性重視設定");
        
        // 結果検証
        assertNotNull(result);
        assertTrue(result.executionTime > 0);
        assertTrue(result.outputSize > 0);
        assertEquals(testData.size(), result.recordCount);
        
        logger.info("互換性重視設定テスト完了: {}", result);
    }
    
    /**
     * パフォーマンステスト実行
     * 
     * @param options エクスポートオプション
     * @param configName 設定名
     * @return パフォーマンス結果
     * @throws IOException 書き込みエラー
     */
    private PerformanceResult executePerformanceTest(ExportOptions options, String configName) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        long startTime = System.currentTimeMillis();
        long startMemory = getUsedMemory();
        
        try {
            // ヘッダー書き込み
            strategy.writeHeader(columns, outputStream, options);
            
            // データ書き込み
            for (Map<String, Object> record : testData) {
                strategy.writeRecord(record, columns, outputStream, options);
            }
            
            // 完了処理
            strategy.finish(outputStream, options);
            
        } finally {
            outputStream.close();
        }
        
        long endTime = System.currentTimeMillis();
        long endMemory = getUsedMemory();
        
        return new PerformanceResult(
            configName,
            endTime - startTime,
            outputStream.size(),
            testData.size(),
            endMemory - startMemory
        );
    }
    
    /**
     * テストデータ生成
     * 
     * @param count 生成件数
     * @return テストデータリスト
     */
    private List<Map<String, Object>> generateTestData(int count) {
        List<Map<String, Object>> data = new ArrayList<>(count);
        Random random = new Random(12345); // 固定シードで再現性確保
        
        for (int i = 0; i < count; i++) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", i + 1);
            record.put("name", "テストユーザー" + (i + 1));
            record.put("email", "user" + (i + 1) + "@example.com");
            record.put("department", "部署" + (random.nextInt(10) + 1));
            record.put("salary", 300000 + random.nextInt(500000));
            data.add(record);
        }
        
        return data;
    }
    
    /**
     * 使用メモリ量取得
     * 
     * @return 使用メモリ量（バイト）
     */
    private long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    /**
     * パフォーマンス結果クラス
     */
    private static class PerformanceResult {
        final String configName;
        final long executionTime;
        final long outputSize;
        final int recordCount;
        final long memoryUsage;
        
        PerformanceResult(String configName, long executionTime, long outputSize, int recordCount, long memoryUsage) {
            this.configName = configName;
            this.executionTime = executionTime;
            this.outputSize = outputSize;
            this.recordCount = recordCount;
            this.memoryUsage = memoryUsage;
        }
        
        @Override
        public String toString() {
            return String.format(
                "%s - 実行時間: %dms, 出力サイズ: %dKB, レコード数: %d, メモリ使用量: %dKB",
                configName, executionTime, outputSize / 1024, recordCount, memoryUsage / 1024
            );
        }
    }
}
