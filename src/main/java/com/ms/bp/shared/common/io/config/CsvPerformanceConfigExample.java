package com.ms.bp.shared.common.io.config;

import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;

/**
 * CSV書き込みパフォーマンス設定例
 * 異なるシナリオに応じた最適化設定を提供
 */
public class CsvPerformanceConfigExample {
    
    /**
     * 標準設定（デフォルト）
     * 一般的な用途に適したバランス型設定
     * 
     * @return 標準設定のExportOptions
     */
    public static ExportOptions createStandardConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .csvWriteBufferSize(BusinessConstants.CSV_WRITE_BUFFER_SIZE)
                .outputStreamBufferSize(BusinessConstants.OUTPUT_STREAM_BUFFER_SIZE)
                .enableBatchWrite(true)
                .enableArrayReuse(true)
                .build();
    }
    
    /**
     * 高性能設定
     * 大容量ファイル処理に最適化された設定
     * メモリ使用量は増加するが、処理速度を最大化
     * 
     * @return 高性能設定のExportOptions
     */
    public static ExportOptions createHighPerformanceConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .csvWriteBufferSize(2000) // バッファサイズを2倍に
                .outputStreamBufferSize(65536) // 64KB バッファ
                .enableBatchWrite(true)
                .enableArrayReuse(true)
                .build();
    }
    
    /**
     * 省メモリ設定
     * メモリ使用量を抑えた設定
     * 処理速度は標準より劣るが、メモリ制約がある環境に適用
     * 
     * @return 省メモリ設定のExportOptions
     */
    public static ExportOptions createLowMemoryConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .csvWriteBufferSize(500) // バッファサイズを半分に
                .outputStreamBufferSize(16384) // 16KB バッファ
                .enableBatchWrite(true)
                .enableArrayReuse(false) // オブジェクトプール無効
                .build();
    }
    
    /**
     * 互換性重視設定
     * 既存システムとの完全互換性を保つ設定
     * パフォーマンス最適化機能を無効化
     * 
     * @return 互換性重視設定のExportOptions
     */
    public static ExportOptions createCompatibilityConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .csvWriteBufferSize(1) // バッチ書き込み実質無効
                .outputStreamBufferSize(8192) // デフォルトバッファサイズ
                .enableBatchWrite(false) // バッチ書き込み無効
                .enableArrayReuse(false) // オブジェクトプール無効
                .build();
    }
    
    /**
     * カスタム設定ビルダー
     * 特定要件に応じた設定をカスタマイズ
     * 
     * @param recordCount 予想レコード数
     * @param memoryConstraint メモリ制約レベル（1:厳しい, 2:普通, 3:緩い）
     * @return カスタマイズされたExportOptions
     */
    public static ExportOptions createCustomConfig(int recordCount, int memoryConstraint) {
        // レコード数に基づくバッファサイズ計算
        int bufferSize;
        int streamBufferSize;
        boolean enableArrayReuse;
        
        if (recordCount < 10000) {
            // 小規模データ
            bufferSize = 500;
            streamBufferSize = 16384; // 16KB
            enableArrayReuse = false;
        } else if (recordCount < 100000) {
            // 中規模データ
            bufferSize = 1000;
            streamBufferSize = 32768; // 32KB
            enableArrayReuse = true;
        } else {
            // 大規模データ
            bufferSize = 2000;
            streamBufferSize = 65536; // 64KB
            enableArrayReuse = true;
        }
        
        // メモリ制約に基づく調整
        switch (memoryConstraint) {
            case 1: // 厳しい制約
                bufferSize = Math.min(bufferSize, 500);
                streamBufferSize = Math.min(streamBufferSize, 16384);
                enableArrayReuse = false;
                break;
            case 2: // 普通の制約
                // デフォルト値を使用
                break;
            case 3: // 緩い制約
                bufferSize = Math.max(bufferSize, 1500);
                streamBufferSize = Math.max(streamBufferSize, 49152); // 48KB
                enableArrayReuse = true;
                break;
        }
        
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .csvWriteBufferSize(bufferSize)
                .outputStreamBufferSize(streamBufferSize)
                .enableBatchWrite(true)
                .enableArrayReuse(enableArrayReuse)
                .build();
    }
    
    /**
     * 設定情報をログ出力用文字列として取得
     * 
     * @param options ExportOptions
     * @return 設定情報文字列
     */
    public static String getConfigSummary(ExportOptions options) {
        return String.format(
            "CSV書き込み設定: bufferSize=%d, streamBuffer=%dKB, batchWrite=%s, arrayReuse=%s",
            options.getCsvWriteBufferSize(),
            options.getOutputStreamBufferSize() / 1024,
            options.isEnableBatchWrite(),
            options.isEnableArrayReuse()
        );
    }
}
