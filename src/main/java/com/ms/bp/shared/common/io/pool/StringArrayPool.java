package com.ms.bp.shared.common.io.pool;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

/**
 * 文字列配列オブジェクトプール
 * メモリ効率向上のための配列再利用機能を提供
 */
public class StringArrayPool {
    private static final Logger logger = LoggerFactory.getLogger(StringArrayPool.class);
    
    private final BlockingQueue<String[]> pool;
    private final int arraySize;
    private final boolean enablePooling;
    
    /**
     * コンストラクタ
     * 
     * @param arraySize 配列サイズ
     * @param maxPoolSize プール最大サイズ
     * @param enablePooling プール機能有効フラグ
     */
    public StringArrayPool(int arraySize, int maxPoolSize, boolean enablePooling) {
        this.arraySize = arraySize;
        int maxPoolSize1 = Math.max(1, maxPoolSize);
        this.enablePooling = enablePooling;
        this.pool = enablePooling ? new ArrayBlockingQueue<>(maxPoolSize1) : null;
        
        logger.debug("StringArrayPool初期化: arraySize={}, maxPoolSize={}, enablePooling={}", 
                    arraySize, maxPoolSize1, enablePooling);
    }
    
    /**
     * 配列をプールから取得
     * プールが空の場合は新しい配列を作成
     * 
     * @return 文字列配列
     */
    public String[] borrowArray() {
        if (!enablePooling) {
            return new String[arraySize];
        }
        
        String[] array = pool.poll();
        if (array == null) {
            array = new String[arraySize];
            logger.trace("新しい配列を作成: size={}", arraySize);
        } else {
            // 配列をクリア（再利用のため）
            clearArray(array);
            logger.trace("プールから配列を取得: size={}", arraySize);
        }
        
        return array;
    }
    
    /**
     * 配列をプールに返却
     * プールが満杯の場合は破棄
     * 
     * @param array 返却する配列
     */
    public void returnArray(String[] array) {
        if (!enablePooling || array == null || array.length != arraySize) {
            return;
        }
        
        // プールに返却（満杯の場合は破棄）
        boolean returned = pool.offer(array);
        if (returned) {
            logger.trace("配列をプールに返却: size={}", arraySize);
        } else {
            logger.trace("プール満杯のため配列を破棄: size={}", arraySize);
        }
    }
    
    /**
     * 配列の内容をクリア
     * 
     * @param array クリアする配列
     */
    private void clearArray(String[] array) {
        Arrays.fill(array, null);
    }

    /**
     * プールをクリア
     */
    public void clear() {
        if (enablePooling) {
            pool.clear();
            logger.debug("StringArrayPoolをクリアしました");
        }
    }
    

}
