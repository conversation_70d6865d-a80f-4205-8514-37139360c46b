package com.ms.bp.shared.common.io.strategy.impl;

import com.ms.bp.shared.common.io.buffer.CsvWriteBuffer;
import com.ms.bp.shared.common.io.options.ExportOptions;
import com.ms.bp.shared.common.io.pool.StringArrayPool;
import com.ms.bp.shared.common.io.strategy.DataWriteStrategy;
import com.opencsv.CSVWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CSV書き込み戦略実装（パフォーマンス最適化版）
 * バッチ書き込み、オブジェクトプール、フィールドマッピングキャッシュによる高速化
 */
public class CsvWriteStrategy<T> implements DataWriteStrategy<T> {
    private static final Logger logger = LoggerFactory.getLogger(CsvWriteStrategy.class);

    // CSV書き込み関連
    private CSVWriter csvWriter;
    private CsvWriteBuffer writeBuffer;

    // パフォーマンス最適化関連
    private StringArrayPool arrayPool;
    private Map<String, String> fieldMappingCache;
    private boolean isInitialized = false;

    // 設定情報
    private ExportOptions currentOptions;
    private List<String> currentColumns;

    @Override
    public void writeHeader(List<String> columns, OutputStream outputStream, ExportOptions options) throws IOException {
        // 初期化処理
        initializeIfNeeded(outputStream, options, columns);

        if (columns != null && !columns.isEmpty()) {
            // フィールドマッピングキャッシュを構築
            buildFieldMappingCache(columns, options);

            // ヘッダー配列を作成
            String[] headerArray = createHeaderArray(columns, options);

            // ヘッダーを書き込み（バッファを使用せず直接書き込み）
            csvWriter.writeNext(headerArray);

            logger.debug("CSVヘッダー書き込み完了: 列数={}", columns.size());
        }
    }

    /**
     * 必要に応じて初期化処理を実行
     *
     * @param outputStream 出力ストリーム
     * @param options エクスポートオプション
     * @param columns 列リスト
     * @throws IOException 初期化エラー
     */
    private void initializeIfNeeded(OutputStream outputStream, ExportOptions options, List<String> columns) throws IOException {
        if (isInitialized) {
            return;
        }

        // 設定情報を保存
        this.currentOptions = options;
        this.currentColumns = columns;

        // CSVWriter初期化（バッファ付きWriter使用）
        BufferedWriter bufferedWriter = new BufferedWriter(
            new OutputStreamWriter(outputStream, StandardCharsets.UTF_8),
            options.getOutputStreamBufferSize()
        );

        this.csvWriter = new CSVWriter(bufferedWriter,
                options.getDelimiter().charAt(0),
                CSVWriter.DEFAULT_QUOTE_CHARACTER,
                CSVWriter.DEFAULT_ESCAPE_CHARACTER,
                CSVWriter.DEFAULT_LINE_END);

        // 書き込みバッファ初期化
        this.writeBuffer = new CsvWriteBuffer(
            csvWriter,
            options.getCsvWriteBufferSize(),
            options.isEnableBatchWrite()
        );

        // オブジェクトプール初期化
        if (options.isEnableArrayReuse() && columns != null) {
            this.arrayPool = new StringArrayPool(
                columns.size(),
                Math.max(10, options.getCsvWriteBufferSize() / 10), // プールサイズはバッファサイズの1/10
                true
            );
        }

        this.isInitialized = true;

        logger.debug("CsvWriteStrategy初期化完了: bufferSize={}, arrayReuse={}",
                    options.getCsvWriteBufferSize(), options.isEnableArrayReuse());
    }

    @Override
    public void writeRecord(Map<String, Object> data, List<String> columns, OutputStream outputStream, ExportOptions options) throws IOException {
        // 初期化処理
        initializeIfNeeded(outputStream, options, columns);

        // 配列を取得（プールから再利用またはNew作成）
        String[] row = arrayPool != null ? arrayPool.borrowArray() : new String[columns.size()];

        try {
            // データを配列に変換（最適化されたロジック）
            populateRowData(row, data, columns);

            // バッファに追加（バッチ書き込み）
            writeBuffer.addRecord(row);

        } finally {
            // 配列をプールに返却（再利用のため）
            if (arrayPool != null) {
                arrayPool.returnArray(row);
            }
        }
    }

    /**
     * 行データを配列に設定
     *
     * @param row 設定先配列
     * @param data データマップ
     * @param columns 列リスト
     */
    private void populateRowData(String[] row, Map<String, Object> data, List<String> columns) {
        for (int i = 0; i < columns.size(); i++) {
            String columnName = columns.get(i);
            Object value = data.get(columnName);

            // null値の効率的な処理
            if (value == null) {
                row[i] = "";
            } else if (value instanceof String) {
                // 文字列の場合はキャストで高速化
                row[i] = (String) value;
            } else {
                // その他の型はtoString()
                row[i] = value.toString();
            }
        }
    }

    @Override
    public void writeFooter(OutputStream outputStream, ExportOptions options) throws IOException {
        // CSVには特定のフッターはありません
    }

    @Override
    public void finish(OutputStream outputStream, ExportOptions options) throws IOException {
        try {
            // バッファ内の残りデータを全て書き込み
            if (writeBuffer != null) {
                writeBuffer.close();
            }

            // オブジェクトプールをクリア
            if (arrayPool != null) {
                arrayPool.clear();
            }

            logger.debug("CsvWriteStrategy処理完了");

        } finally {
            // リソースクリーンアップ
            cleanup();
        }
    }

    /**
     * リソースクリーンアップ
     */
    private void cleanup() {
        try {
            if (csvWriter != null) {
                csvWriter.close();
            }
        } catch (IOException e) {
            logger.warn("CSVWriterクローズ時にエラーが発生しました", e);
        } finally {
            csvWriter = null;
            writeBuffer = null;
            arrayPool = null;
            fieldMappingCache = null;
            isInitialized = false;
            currentOptions = null;
            currentColumns = null;
        }
    }

    /**
     * フィールドマッピングキャッシュを構築
     *
     * @param columns 列リスト
     * @param options エクスポートオプション
     */
    private void buildFieldMappingCache(List<String> columns, ExportOptions options) {
        if (!options.isEnableFieldMapping() || options.getFieldHeaderMapping() == null) {
            return;
        }

        this.fieldMappingCache = new HashMap<>(columns.size());
        Map<String, String> mapping = options.getFieldHeaderMapping();

        for (String fieldName : columns) {
            String mappedHeader = mapping.get(fieldName);
            fieldMappingCache.put(fieldName, mappedHeader != null ? mappedHeader : fieldName);
        }

        logger.debug("フィールドマッピングキャッシュ構築完了: サイズ={}", fieldMappingCache.size());
    }

    /**
     * ヘッダー配列を作成
     *
     * @param columns 列リスト
     * @param options エクスポートオプション
     * @return ヘッダー配列
     */
    private String[] createHeaderArray(List<String> columns, ExportOptions options) {
        String[] headerArray = new String[columns.size()];

        if (options.isEnableFieldMapping() && fieldMappingCache != null) {
            // キャッシュを使用してヘッダーを変換
            for (int i = 0; i < columns.size(); i++) {
                String fieldName = columns.get(i);
                headerArray[i] = fieldMappingCache.get(fieldName);
            }
        } else {
            // 直接コピー
            for (int i = 0; i < columns.size(); i++) {
                headerArray[i] = columns.get(i);
            }
        }

        return headerArray;
    }
}